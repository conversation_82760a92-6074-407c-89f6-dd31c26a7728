"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm, useFieldArray } from "react-hook-form"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { createSalesP<PERSON>, updateSalesPlan } from "@/lib/directus"
import type { Kang<PERSON>der, Produk, SalesPlanned } from "@/lib/directus"

const formSchema = z.object({
  kangider: z.string({
    required_error: "Please select a Kang Ider",
  }),
  date: z.string().min(1, {
    message: "Please select a date",
  }),
  status: z.enum(["draft", "published", "load ider"], {
    required_error: "Please select a status",
  }),
  items: z.array(
    z.object({
      produk: z.string(),
      quantity: z.coerce
        .number()
        .min(0, "Quantity must be 0 or greater")
        .transform(val => (isNaN(val) ? 0 : val))
    })
  ),
})

interface SalesPlanFormProps {
  kangIders: KangIder[]
  products: Produk[]
  mode: "add" | "edit"
  salesPlan?: SalesPlanned
}

export function SalesPlanForm({ kangIders, products, mode, salesPlan }: SalesPlanFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [totalCups, setTotalCups] = useState(0)
  const [showStockAlert, setShowStockAlert] = useState(false)
  const [pendingFormValues, setPendingFormValues] = useState<z.infer<typeof formSchema> | null>(null)

  console.log("Form component rendered with mode:", mode)
  console.log("Sales plan data:", salesPlan)
  console.log("Kangiders data:", kangIders)
  console.log("Products data:", products)

  // Helper function to get kangider ID
  const getKangiderId = () => {
    if (!salesPlan?.kangider) return ""
    if (typeof salesPlan.kangider === 'string') return salesPlan.kangider
    if (typeof salesPlan.kangider === 'object' && 'id' in salesPlan.kangider) {
      return (salesPlan.kangider as any).id
    }
    return ""
  }

  // Helper function to get product ID from item
  const getItemProductId = (item: any) => {
    if (typeof item.produk === 'string') return item.produk
    if (typeof item.produk === 'object' && item.produk && 'id' in item.produk) {
      return (item.produk as any).id
    }
    return null
  }

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      kangider: getKangiderId(),
      date: salesPlan?.date || new Date().toISOString().split("T")[0],
      status: salesPlan?.status || "draft",
      items: products.map((product) => ({
        produk: String(product.id),
        quantity: salesPlan?.items?.find((item) => {
          const itemProdukId = getItemProductId(item)
          return itemProdukId === product.id
        })?.quantity || 0,
      })),
    },
  })

  // Debug form state
  useEffect(() => {
    console.log("Form state:", form.getValues())
    if (!form.formState.isValid) {
      console.log("Form validation errors:", form.formState.errors)
    }
  }, [form, form.formState])

  const { fields } = useFieldArray({
    control: form.control,
    name: "items",
  })

  const watchedItems = form.watch("items")

  useEffect(() => {
    const total = watchedItems.reduce((sum, item) => sum + (Number(item.quantity) || 0), 0)
    setTotalCups(total)
  }, [watchedItems])

  async function handleFormSubmit(values: z.infer<typeof formSchema>) {
    // If status is changing to "load ider" and not already submitting, show confirmation
    if (values.status === "load ider" && !isSubmitting) {
      setPendingFormValues(values);
      setShowStockAlert(true);
      return;
    }
    
    // Otherwise proceed with normal submission
    submitForm(values);
  }
  
  async function submitForm(values: z.infer<typeof formSchema>) {
    console.log("Form submission started")
    console.log("Detailed form values:", {
      kangider: values.kangider,
      date: values.date,
      status: values.status,
      items: values.items
    })

    if (!form.formState.isValid) {
      console.log("Form is invalid:", form.formState.errors)
      setError("Please fix the validation errors before submitting")
      return
    }
    
    setIsSubmitting(true)
    setError(null)

    try {
      // Filter out items with zero quantity and ensure produk is a string
      const nonZeroItems = values.items
        .filter(item => Number(item.quantity) > 0)
        .map(item => ({
          produk: String(item.produk),
          quantity: Number(item.quantity)
        }))

      console.log("Non-zero items:", nonZeroItems)

      if (nonZeroItems.length === 0) {
        setError("Please add at least one product quantity")
        console.log("Validation failed: No products with quantity")
        return
      }

      const formData = {
        kangider: values.kangider,
        date: values.date,
        status: values.status,
        total_cup: totalCups,
        items: nonZeroItems
      }

      console.log("Final form data being sent:", formData)

      if (mode === "add") {
        console.log("Attempting to create new sales plan...")
        try {
          const response = await fetch("/api/sales-plans", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(formData)
          });

          const result = await response.json();
          console.log("API Response:", result);

          if (!response.ok) {
            throw new Error(result.message || "Failed to create sales plan");
          }

          console.log("Sales plan created successfully");
          router.push("/sales");
          router.refresh();
        } catch (error) {
          console.error("Error creating sales plan:", error);
          throw error;
        }
      } else if (salesPlan?.id) {
        console.log("Attempting to update sales plan...");
        
        // If status is changing to "load ider", show a special message
        const isChangingToLoadIder = values.status === "load ider" && salesPlan.status !== "load ider";
        if (isChangingToLoadIder) {
          console.log("Status changing to 'load ider' - production stock will be reduced");
        }
        
        const result = await updateSalesPlan(salesPlan.id, formData);
        console.log("Update result:", result);
        
        router.push("/sales");
        router.refresh();
      }
    } catch (err) {
      console.error("Detailed form submission error:", err)
      const errorMessage = err instanceof Error 
        ? err.message 
        : typeof err === 'object' && err !== null && 'message' in err 
          ? String(err.message) 
          : "Failed to save sales plan. Please try again."
      setError(errorMessage)
      console.error("Error details:", err)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Add form state monitoring
  const formState = form.formState
  useEffect(() => {
    console.log("Form is valid:", formState.isValid)
    console.log("Form errors:", formState.errors)
  }, [formState])

  // Monitor form values
  const formValues = form.watch()
  useEffect(() => {
    console.log("Current form values:", formValues)
  }, [formValues])

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-destructive/15 text-destructive p-3 rounded-md">
          {error}
        </div>
      )}

      <AlertDialog open={showStockAlert} onOpenChange={setShowStockAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Stock Reduction</AlertDialogTitle>
            <AlertDialogDescription>
              Changing status to "Load Ider" will reduce production stock for all products in this sales plan.
              This action cannot be undone. Are you sure you want to continue?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowStockAlert(false)}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={() => {
                setShowStockAlert(false);
                if (pendingFormValues) {
                  submitForm(pendingFormValues);
                }
              }}
            >
              Yes, Reduce Stock
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Card>
        <CardHeader>
          <CardTitle>Sales Plan Details</CardTitle>
          <CardDescription>Enter the details for this sales plan</CardDescription>
        </CardHeader>
        <Form {...form}>
          <form 
            onSubmit={(e) => {
              e.preventDefault();
              console.log("Form submit event triggered");
              const formData = form.getValues();
              console.log("Current form data:", formData);
              if (!form.formState.isValid) {
                console.log("Form validation failed:", form.formState.errors);
                setError("Please fix all validation errors before submitting");
                return;
              }
              form.handleSubmit(handleFormSubmit)(e);
            }}
            noValidate
          >
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="kangider"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kang Ider</FormLabel>
                      <Select 
                        onValueChange={(value) => {
                          console.log("Kang Ider selected:", value);
                          field.onChange(value);
                        }} 
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a Kang Ider" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {kangIders.map((kangIder) => (
                            <SelectItem key={kangIder.id} value={kangIder.id}>
                              {kangIder.nama}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="published">Published</SelectItem>
                          <SelectItem value="load ider">Load Ider</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Product Quantities</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {fields.map((field, index) => {
                    // Convert produk ID to string for proper comparison
                    const productId = String(field.produk)
                    const product = products.find((p) => String(p.id) === productId)
                    return (
                      <div key={field.id} className="flex items-center border rounded-md p-3 gap-4">
                        <div className="flex-1">
                          <span className="font-medium">
                            {product?.nama_produk || `Product ${productId}`}
                          </span>
                        </div>
                        <div className="w-20">
                          <FormField
                            control={form.control}
                            name={`items.${index}.quantity`}
                            render={({ field }) => (
                              <FormItem className="space-y-0">
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    className="text-right"
                                    {...field}
                                    onChange={(e) => field.onChange(Number.parseInt(e.target.value) || 0)}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    )
                  })}
                </div>
                <div className="flex justify-end mt-4 border-t pt-4">
                  <div className="flex items-center gap-4">
                    <span className="font-medium">Total Cups:</span>
                    <span className="text-xl font-bold">{totalCups}</span>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button 
                variant="outline" 
                type="button" 
                onClick={() => {
                  console.log("Cancel button clicked");
                  router.push("/sales");
                }}
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                disabled={isSubmitting}
                onClick={() => console.log("Submit button clicked")}
              >
                {isSubmitting ? "Saving..." : mode === "add" ? "Create Sales Plan" : "Update Sales Plan"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  )
}
