import { getKangIders, getProduk, getSalesPlans } from "@/lib/directus"
import { SalesPlanForm } from "../../sales-plan-form"
import { notFound } from "next/navigation"

export default async function EditSalesPlanPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  const resolvedParams = await params
  const [kangIders, products, salesPlans] = await Promise.all([
    getKangIders(),
    getProduk(),
    getSalesPlans({ id: { _eq: resolvedParams.id } })
  ])

  const salesPlan = salesPlans[0]

  if (!salesPlan) {
    notFound()
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Edit Sales Plan</h1>
        <p className="text-muted-foreground">Update the sales distribution plan</p>
      </div>

      <SalesPlanForm
        kangIders={kangIders}
        products={products}
        mode="edit"
        salesPlan={salesPlan}
      />
    </div>
  )
}
