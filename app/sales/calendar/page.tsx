"use client"

import { useState, useEffect } from 'react'
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, addMonths, subMonths } from 'date-fns'
import { ChevronLeft, ChevronRight, Plus, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { getSalesPlans, getProduk, getKangIders } from '@/lib/directus'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import type { SalesPlanned, Produk, KangIder } from '@/lib/directus'

export default function SalesCalendarPage() {
  const router = useRouter()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [salesPlans, setSalesPlans] = useState<SalesPlanned[]>([])
  const [products, setProducts] = useState<Produk[]>([])
  const [kangiders, setKangiders] = useState<KangIder[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const monthStart = startOfMonth(currentDate)
  const monthEnd = endOfMonth(currentDate)
  const calendarDays = eachDayOfInterval({ start: monthStart, end: monthEnd })

  useEffect(() => {
    fetchData()
  }, [currentDate])

  const fetchData = async () => {
    try {
      setIsLoading(true)
      const startDate = format(monthStart, 'yyyy-MM-dd')
      const endDate = format(monthEnd, 'yyyy-MM-dd')

      const [salesData, productsData, kangidersData] = await Promise.all([
        getSalesPlans({
          date: {
            _between: [startDate, endDate]
          }
        }),
        getProduk(),
        getKangIders()
      ])

      setSalesPlans(salesData || [])
      setProducts(productsData || [])
      setKangiders(kangidersData || [])
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getSalesForDate = (date: Date) => {
    const dateStr = format(date, 'yyyy-MM-dd')
    return salesPlans.filter(plan => plan.date === dateStr)
  }

  const getKangiderName = (kangiderId: string) => {
    const kangider = kangiders.find(k => k.id === kangiderId)
    return kangider?.nama || 'Unknown Kang Ider'
  }

  const getTotalQuantityForDate = (date: Date) => {
    const sales = getSalesForDate(date)
    return sales.reduce((total, sale) => total + (sale.total_cup || 0), 0)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-blue-100 text-blue-800'
      case 'published': return 'bg-green-100 text-green-800'
      case 'load ider': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-emerald-100 text-emerald-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => direction === 'prev' ? subMonths(prev, 1) : addMonths(prev, 1))
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading calendar...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Sales Calendar</h1>
            <p className="text-muted-foreground">Monthly view of sales plans</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/sales/add">
              <Plus className="mr-2 h-4 w-4" />
              Create Sales Plan
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{format(currentDate, 'MMMM yyyy')}</CardTitle>
              <CardDescription>Sales plans for the month</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('prev')}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentDate(new Date())}
              >
                Today
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('next')}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-1 mb-4">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="p-2 text-center font-medium text-sm text-muted-foreground">
                {day}
              </div>
            ))}
          </div>
          
          <div className="grid grid-cols-7 gap-1">
            {calendarDays.map(day => {
              const salesForDay = getSalesForDate(day)
              const totalQuantity = getTotalQuantityForDate(day)
              const isToday = isSameDay(day, new Date())
              const isCurrentMonth = isSameMonth(day, currentDate)
              
              return (
                <div
                  key={day.toISOString()}
                  className={`
                    min-h-[140px] p-2 border rounded-lg hover:bg-gray-50 transition-colors relative
                    ${isToday ? 'bg-blue-50 border-blue-200' : 'border-gray-200'}
                    ${!isCurrentMonth ? 'opacity-50' : ''}
                  `}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${isToday ? 'text-blue-600' : 'text-gray-900'}`}>
                      {format(day, 'd')}
                    </span>
                    <div className="flex items-center gap-1">
                      {totalQuantity > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {totalQuantity}
                        </Badge>
                      )}
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0 hover:bg-blue-100"
                        onClick={(e) => {
                          e.stopPropagation()
                          router.push(`/sales/add?date=${format(day, 'yyyy-MM-dd')}`)
                        }}
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-1">
                    {salesForDay.slice(0, 2).map(sale => (
                      <div
                        key={sale.id}
                        className={`text-xs p-1 rounded cursor-pointer hover:opacity-80 ${getStatusColor(sale.status)}`}
                        title={`${getKangiderName(sale.kangider)} - ${sale.total_cup} cups`}
                        onClick={(e) => {
                          e.stopPropagation()
                          router.push(`/sales/view/${sale.id}`)
                        }}
                      >
                        <div className="font-medium truncate">
                          {getKangiderName(sale.kangider)}
                        </div>
                        <div className="text-xs opacity-75">
                          {sale.total_cup} cups
                        </div>
                      </div>
                    ))}
                    {salesForDay.length > 2 && (
                      <div
                        className="text-xs text-muted-foreground cursor-pointer hover:text-blue-600"
                        onClick={() => router.push(`/sales?date=${format(day, 'yyyy-MM-dd')}`)}
                      >
                        +{salesForDay.length - 2} more
                      </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Status Legend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded bg-blue-100 border border-blue-200"></div>
              <span className="text-sm">Draft</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded bg-green-100 border border-green-200"></div>
              <span className="text-sm">Published</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded bg-yellow-100 border border-yellow-200"></div>
              <span className="text-sm">Load Ider</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded bg-emerald-100 border border-emerald-200"></div>
              <span className="text-sm">Completed</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded bg-red-100 border border-red-200"></div>
              <span className="text-sm">Cancelled</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
