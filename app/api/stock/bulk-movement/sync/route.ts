import { NextRequest, NextResponse } from 'next/server'
import { accurateSync } from '@/lib/accurate-sync'

const DIRECTUS_URL = process.env.NEXT_PUBLIC_DIRECTUS_URL!
const DIRECTUS_TOKEN = process.env.DIRECTUS_TOKEN!

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, reference_number, type } = body

    console.log('🔄 Manual sync request:', { id, reference_number, type })

    // Get token from request headers
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '') || DIRECTUS_TOKEN

    if (type === 'single_item' && id) {
      // Sync single item
      const result = await syncSingleItem(id, token)
      return NextResponse.json(result)
    } else if (type === 'bulk_movement' && reference_number) {
      // Sync entire bulk movement
      const result = await syncBulkMovement(reference_number, token)
      return NextResponse.json(result)
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid request parameters'
      }, { status: 400 })
    }

  } catch (error) {
    console.error('❌ Error in sync API:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}

async function syncSingleItem(movementId: string, token: string) {
  try {
    // Get the stock movement item using fetch
    const response = await fetch(`${DIRECTUS_URL}/items/StockMovement?filter[id][_eq]=${movementId}&fields=*,produk_gudang.*`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch movement: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    const movements = result.data

    if (!movements || movements.length === 0) {
      throw new Error('Stock movement not found')
    }

    const movement = movements[0]

    // Check if already synced
    if (movement.synced) {
      return {
        success: true,
        message: 'Item is already synced',
        already_synced: true
      }
    }

    // Sync to Accurate using real implementation
    const syncResult = await accurateSync.syncStockMovement(movement.id)

    if (syncResult.success) {
      // Update the movement as synced using fetch
      const updateResponse = await fetch(`${DIRECTUS_URL}/items/StockMovement/${movementId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          synced: true,
          accurate_id: syncResult.accurateId,
          sync_error: null
        })
      })

      if (!updateResponse.ok) {
        throw new Error(`Failed to update movement: ${updateResponse.status} ${updateResponse.statusText}`)
      }

      return {
        success: true,
        message: 'Item synced successfully',
        accurate_id: syncResult.accurateId
      }
    } else {
      // Update with sync error using fetch
      const updateResponse = await fetch(`${DIRECTUS_URL}/items/StockMovement/${movementId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          synced: false,
          sync_error: syncResult.error
        })
      })

      if (!updateResponse.ok) {
        console.warn(`Failed to update sync error: ${updateResponse.status} ${updateResponse.statusText}`)
      }

      throw new Error(`Sync failed: ${syncResult.error}`)
    }

  } catch (error) {
    console.error('❌ Error syncing single item:', error)
    throw error
  }
}

async function syncBulkMovement(referenceNumber: string, token: string) {
  try {
    // Get all unsynced movements for this reference number using fetch
    const response = await fetch(`${DIRECTUS_URL}/items/StockMovement?filter[reference_number][_eq]=${encodeURIComponent(referenceNumber)}&filter[synced][_eq]=false&fields=*,produk_gudang.*`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch movements: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    const movements = result.data

    if (!movements || movements.length === 0) {
      return {
        success: true,
        message: 'All items in this bulk movement are already synced',
        synced_count: 0
      }
    }

    let syncedCount = 0
    const errors: string[] = []

    // Use bulk sync for better performance and consistency
    try {
      const bulkSyncResult = await accurateSync.syncBulkMovements(referenceNumber)

      if (bulkSyncResult.success) {
        syncedCount = movements.length
      } else {
        errors.push(bulkSyncResult.error || 'Bulk sync failed')
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      errors.push(`Bulk sync error: ${errorMsg}`)
    }

    return {
      success: syncedCount > 0,
      message: `Synced ${syncedCount} out of ${movements.length} items`,
      synced_count: syncedCount,
      total_items: movements.length,
      errors: errors.length > 0 ? errors : undefined
    }

  } catch (error) {
    console.error('❌ Error syncing bulk movement:', error)
    throw error
  }
}


