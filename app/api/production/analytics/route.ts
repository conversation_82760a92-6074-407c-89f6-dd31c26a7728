import { NextRequest, NextResponse } from 'next/server'
import directus from '@/lib/directus'
import { readItems } from '@directus/sdk'
import { getToken } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Get token from request
    const token = request.headers.get('authorization')?.split(' ')[1]
    
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    // Verify token
    try {
      await getToken(token)
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }
    
    // Get query parameters
    const searchParams = request.nextUrl.searchParams
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')
    
    // Default to today if no dates provided
    const today = new Date().toISOString().split('T')[0]
    const filterStartDate = startDate || today
    const filterEndDate = endDate || today
    
    // Fetch all required data
    const [products, dailyProduction, productionStock, distributions, distributionChannels] = await Promise.all([
      directus.request(readItems('produk', { fields: ['*'] })),
      directus.request(readItems('daily_production', {
        filter: {
          date: {
            _between: [filterStartDate, filterEndDate]
          }
        },
        fields: ['*']
      })),
      directus.request(readItems('production_stock', { fields: ['*'] })),
      directus.request(readItems('production_distributions', {
        filter: {
          date: {
            _between: [filterStartDate, filterEndDate]
          }
        },
        fields: ['*']
      })),
      directus.request(readItems('distribution_channels', { fields: ['*'] }))
    ])
    
    // Calculate production analytics
    const productionAnalytics = {
      total: dailyProduction.reduce((sum: number, prod: any) => sum + prod.quantity_produced, 0),
      target: dailyProduction.reduce((sum: number, prod: any) => sum + (prod.target_quantity || 0), 0),
      variance: 0,
      products: [] as any[]
    }
    
    productionAnalytics.variance = productionAnalytics.target > 0 
      ? Math.round(((productionAnalytics.total - productionAnalytics.target) / productionAnalytics.target) * 100)
      : 0
    
    // Group by product
    const productMap = new Map()
    dailyProduction.forEach((prod: any) => {
      const product = products.find((p: any) => p.id === prod.produk_id)
      const productName = product?.nama_produk || 'Unknown Product'
      
      if (!productMap.has(prod.produk_id)) {
        productMap.set(prod.produk_id, {
          name: productName,
          produced: 0,
          target: 0,
          variance: 0
        })
      }
      
      const item = productMap.get(prod.produk_id)
      item.produced += prod.quantity_produced
      item.target += prod.target_quantity || 0
    })
    
    // Calculate variance for each product
    productMap.forEach((item) => {
      item.variance = item.target > 0 
        ? Math.round(((item.produced - item.target) / item.target) * 100)
        : 0
    })
    
    productionAnalytics.products = Array.from(productMap.values())
    
    // Calculate distribution analytics
    const distributionAnalytics = {
      total: distributions.reduce((sum: number, dist: any) => sum + dist.quantity, 0),
      channels: [] as any[]
    }
    
    // Group by channel
    const channelMap = new Map()
    distributions.forEach((dist: any) => {
      const channel = distributionChannels.find((c: any) => c.id === dist.distribution_channel_id)
      const channelName = channel?.name || 'Unknown Channel'
      const channelType = channel?.type || 'unknown'
      
      if (!channelMap.has(dist.distribution_channel_id)) {
        channelMap.set(dist.distribution_channel_id, {
          name: channelName,
          type: channelType,
          distributed: 0,
          percentage: 0
        })
      }
      
      const item = channelMap.get(dist.distribution_channel_id)
      item.distributed += dist.quantity
    })
    
    // Calculate percentages
    channelMap.forEach((item) => {
      item.percentage = distributionAnalytics.total > 0 
        ? Math.round((item.distributed / distributionAnalytics.total) * 100 * 10) / 10
        : 0
    })
    
    distributionAnalytics.channels = Array.from(channelMap.values())
    
    // Calculate returns analytics
    const totalReturned = distributions.reduce((sum: number, dist: any) => sum + (dist.returned_quantity || 0), 0)
    const returnRate = distributionAnalytics.total > 0 
      ? Math.round((totalReturned / distributionAnalytics.total) * 100 * 10) / 10
      : 0
    
    const returnsAnalytics = {
      total: totalReturned,
      rate: returnRate,
      reasons: [
        { reason: "Tidak laku", count: Math.floor(totalReturned * 0.6), percentage: 60 },
        { reason: "Rusak", count: Math.floor(totalReturned * 0.25), percentage: 25 },
        { reason: "Kadaluarsa", count: Math.floor(totalReturned * 0.15), percentage: 15 }
      ]
    }
    
    // Calculate efficiency metrics
    const productionEfficiency = productionAnalytics.target > 0 
      ? Math.round((productionAnalytics.total / productionAnalytics.target) * 100)
      : 0
    
    const distributionRate = productionAnalytics.total > 0 
      ? Math.round((distributionAnalytics.total / productionAnalytics.total) * 100)
      : 0
    
    const overallScore = Math.round((productionEfficiency + distributionRate + (100 - returnRate)) / 3)
    
    const efficiency = {
      productionEfficiency,
      distributionRate,
      returnRate,
      overallScore
    }
    
    // Calculate stock analytics
    const stockAnalytics = {
      totalStock: productionStock.reduce((sum: number, stock: any) => sum + stock.quantity, 0),
      availableStock: productionStock.reduce((sum: number, stock: any) => sum + stock.available_quantity, 0),
      reservedStock: productionStock.reduce((sum: number, stock: any) => sum + stock.reserved_quantity, 0),
      stockByProduct: productionStock.map((stock: any) => {
        const product = products.find((p: any) => p.id === stock.produk_id)
        return {
          product_name: product?.nama_produk || 'Unknown Product',
          total: stock.quantity,
          available: stock.available_quantity,
          reserved: stock.reserved_quantity
        }
      })
    }
    
    const analyticsData = {
      period: `${filterStartDate} to ${filterEndDate}`,
      production: productionAnalytics,
      distribution: distributionAnalytics,
      returns: returnsAnalytics,
      efficiency,
      stock: stockAnalytics,
      summary: {
        totalProduced: productionAnalytics.total,
        totalDistributed: distributionAnalytics.total,
        totalReturned: totalReturned,
        totalInStock: stockAnalytics.availableStock,
        activeChannels: distributionChannels.filter((c: any) => c.status === 'active').length,
        activeProducts: products.length
      }
    }
    
    return NextResponse.json({
      success: true,
      data: analyticsData
    })
  } catch (error) {
    console.error('Error fetching analytics data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    )
  }
}
