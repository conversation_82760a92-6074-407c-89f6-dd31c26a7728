import { NextRequest, NextResponse } from 'next/server'

const DIRECTUS_URL = process.env.DIRECTUS_URL || 'http://localhost:8055'

export const dynamic = 'force-dynamic'

// GET - Fetch distributions
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { searchParams } = new URL(request.url)
    
    const date = searchParams.get('date')
    const channel_id = searchParams.get('channel_id')
    const status = searchParams.get('status')

    // Build filter
    const filters = []
    if (date) filters.push(`filter[date][_eq]=${date}`)
    if (channel_id) filters.push(`filter[distribution_channel_id][_eq]=${channel_id}`)
    if (status) filters.push(`filter[status][_eq]=${status}`)

    const filterQuery = filters.length > 0 ? `?${filters.join('&')}` : ''

    console.log('🔄 Fetching distributions with filters:', filterQuery)

    const fieldsParam = filterQuery ? '&fields=*,produk_id.nama_produk,distribution_channel_id.name' : '?fields=*,produk_id.nama_produk,distribution_channel_id.name'
    const response = await fetch(`${DIRECTUS_URL}/items/production_distributions${filterQuery}${fieldsParam}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      throw new Error(`Directus API error: ${response.status}`)
    }

    const data = await response.json()
    
    return NextResponse.json({
      success: true,
      data: data.data || []
    })

  } catch (error) {
    console.error('❌ Error fetching distributions:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch distributions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST - Create distribution
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const body = await request.json()
    const { produk_id, distributions } = body

    console.log('🔄 Creating distributions for product:', produk_id)

    if (!produk_id || !distributions || !Array.isArray(distributions)) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data' },
        { status: 400 }
      )
    }

    const today = new Date().toISOString().split('T')[0]
    const results = []

    // Check production stock availability
    const stockResponse = await fetch(`${DIRECTUS_URL}/items/production_stock?filter[produk_id][_eq]=${produk_id}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!stockResponse.ok) {
      throw new Error('Failed to check stock availability')
    }

    const stockData = await stockResponse.json()
    const stock = stockData.data?.[0]

    if (!stock) {
      return NextResponse.json(
        { success: false, error: 'Product stock not found' },
        { status: 404 }
      )
    }

    // Calculate total quantity needed
    const totalQuantityNeeded = distributions.reduce((sum: number, dist: any) => sum + (dist.quantity || 0), 0)

    if (totalQuantityNeeded > stock.available_quantity) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Insufficient stock. Available: ${stock.available_quantity}, Needed: ${totalQuantityNeeded}` 
        },
        { status: 400 }
      )
    }

    // Create distribution records
    for (const dist of distributions) {
      const distributionData = {
        produk_id,
        distribution_channel_id: dist.channel_id,
        quantity: dist.quantity,
        date: today,
        status: 'planned',
        notes: dist.notes || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const createResponse = await fetch(`${DIRECTUS_URL}/items/production_distributions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(distributionData)
      })

      if (!createResponse.ok) {
        const errorData = await createResponse.json()
        throw new Error(`Failed to create distribution: ${errorData.errors?.[0]?.message || 'Unknown error'}`)
      }

      const createdDistribution = await createResponse.json()
      results.push(createdDistribution.data)
    }

    // Update production stock (reserve quantity)
    const updatedStock = {
      reserved_quantity: stock.reserved_quantity + totalQuantityNeeded,
      available_quantity: stock.available_quantity - totalQuantityNeeded,
      last_updated: new Date().toISOString()
    }

    const updateStockResponse = await fetch(`${DIRECTUS_URL}/items/production_stock/${stock.id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updatedStock)
    })

    if (!updateStockResponse.ok) {
      console.error('⚠️ Failed to update stock, but distributions were created')
    }

    console.log('✅ Created', results.length, 'distributions successfully')

    return NextResponse.json({
      success: true,
      data: results,
      message: `Created ${results.length} distribution(s) successfully`
    })

  } catch (error) {
    console.error('❌ Error creating distributions:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create distributions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// PATCH - Update distribution status
export async function PATCH(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const body = await request.json()
    const { id, status, notes } = body

    console.log('🔄 Updating distribution status:', { id, status })

    if (!id || !status) {
      return NextResponse.json(
        { success: false, error: 'Distribution ID and status are required' },
        { status: 400 }
      )
    }

    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    }

    if (status === 'distributed') {
      updateData.distributed_at = new Date().toISOString()
    }

    if (notes) {
      updateData.notes = notes
    }

    const response = await fetch(`${DIRECTUS_URL}/items/production_distributions/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`Failed to update distribution: ${errorData.errors?.[0]?.message || 'Unknown error'}`)
    }

    const updatedDistribution = await response.json()

    console.log('✅ Distribution status updated successfully')

    return NextResponse.json({
      success: true,
      data: updatedDistribution.data,
      message: 'Distribution status updated successfully'
    })

  } catch (error) {
    console.error('❌ Error updating distribution:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update distribution',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
