import { NextRequest, NextResponse } from 'next/server'

const DIRECTUS_URL = process.env.DIRECTUS_URL || 'http://localhost:8055'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Fetching products for production...')

    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')

    const response = await fetch(`${DIRECTUS_URL}/items/produk?filter[status][_eq]=published&sort=order_menu&fields=*`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      throw new Error(`Directus API error: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ Products fetched successfully:', result.data?.length || 0)

    return NextResponse.json({
      success: true,
      data: result.data || []
    })
  } catch (error) {
    console.error('❌ Error fetching products:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch products',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
