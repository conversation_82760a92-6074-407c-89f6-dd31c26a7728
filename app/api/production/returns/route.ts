import { NextRequest, NextResponse } from 'next/server'

const DIRECTUS_URL = process.env.DIRECTUS_URL || 'http://localhost:8055'

export const dynamic = 'force-dynamic'

// GET - Fetch returns with production date analysis
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { searchParams } = new URL(request.url)

    const distribution_id = searchParams.get('distribution_id')
    const date = searchParams.get('date')
    const groupByProductionDate = searchParams.get('group_by_production_date') === 'true'

    // Build filter for returns
    const filters = []
    if (distribution_id) filters.push(`filter[distribution_id][_eq]=${distribution_id}`)
    if (date) filters.push(`filter[returned_at][_gte]=${date}`)

    const filterQuery = filters.length > 0 ? `?${filters.join('&')}` : ''

    console.log('🔄 Fetching returns with filters:', filterQuery)

    // Get returns data
    const response = await fetch(`${DIRECTUS_URL}/items/production_returns${filterQuery}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      throw new Error(`Directus API error: ${response.status}`)
    }

    const returnsData = await response.json()
    const returns = returnsData.data || []

    // If grouping by production date is requested, enrich with production data
    if (groupByProductionDate && returns.length > 0) {
      // Get distribution data to find product IDs
      const distributionIds = [...new Set(returns.map((r: any) => r.distribution_id))]
      const distributionPromises = distributionIds.map(id =>
        fetch(`${DIRECTUS_URL}/items/production_distributions/${id}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        }).then(res => res.json())
      )

      const distributionsData = await Promise.all(distributionPromises)
      const distributions = distributionsData.map(d => d.data)

      // Get daily production data
      const productIds = [...new Set(distributions.map((d: any) => d.produk_id))]
      const productionFilters = productIds.map(id => `filter[produk_id][_eq]=${id}`).join('&')

      const productionResponse = await fetch(`${DIRECTUS_URL}/items/daily_production?${productionFilters}&fields=*,produk.nama_produk`, {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      const productionData = await productionResponse.json()
      const productions = productionData.data || []

      // Enrich returns with production date information
      const enrichedReturns = returns.map((returnItem: any) => {
        const distribution = distributions.find((d: any) => d.id === returnItem.distribution_id)
        if (!distribution) return returnItem

        // Find the production record for this product closest to the return date
        const productProductions = productions.filter((p: any) => p.produk_id === distribution.produk_id)
        const returnDate = new Date(returnItem.returned_at)

        let closestProduction = null
        let minDiff = Infinity

        for (const prod of productProductions) {
          const prodDate = new Date(prod.date || prod.created_at)
          const diff = Math.abs(returnDate.getTime() - prodDate.getTime())
          if (diff < minDiff && prodDate <= returnDate) {
            minDiff = diff
            closestProduction = prod
          }
        }

        let daysBetween = null
        if (closestProduction) {
          const prodDate = new Date(closestProduction.date || closestProduction.created_at)
          daysBetween = Math.floor((returnDate.getTime() - prodDate.getTime()) / (1000 * 60 * 60 * 24))
        }

        return {
          ...returnItem,
          distribution,
          production_record: closestProduction,
          days_from_production: daysBetween,
          product_name: closestProduction?.produk?.nama_produk || 'Unknown Product'
        }
      })

      // Group by production date
      const returnsByProductionDate: any = {}

      enrichedReturns.forEach((returnItem: any) => {
        if (!returnItem.production_record) return

        const prodDate = returnItem.production_record.date || returnItem.production_record.created_at
        const dateKey = new Date(prodDate).toISOString().split('T')[0]

        if (!returnsByProductionDate[dateKey]) {
          returnsByProductionDate[dateKey] = {
            production_date: prodDate,
            returns: [],
            total_returned: 0,
            products: {}
          }
        }

        returnsByProductionDate[dateKey].returns.push(returnItem)
        returnsByProductionDate[dateKey].total_returned += returnItem.quantity

        // Group by product
        const productId = returnItem.distribution.produk_id
        if (!returnsByProductionDate[dateKey].products[productId]) {
          returnsByProductionDate[dateKey].products[productId] = {
            produk_id: productId,
            product_name: returnItem.product_name,
            total_returned: 0,
            returns: []
          }
        }

        returnsByProductionDate[dateKey].products[productId].total_returned += returnItem.quantity
        returnsByProductionDate[dateKey].products[productId].returns.push(returnItem)
      })

      // Convert to array and sort by date
      const returnsByDateArray = Object.values(returnsByProductionDate).sort((a: any, b: any) => {
        return new Date(b.production_date).getTime() - new Date(a.production_date).getTime()
      })

      return NextResponse.json({
        success: true,
        data: enrichedReturns,
        returnsByDate: returnsByDateArray
      })
    }

    return NextResponse.json({
      success: true,
      data: returns
    })

  } catch (error) {
    console.error('❌ Error fetching returns:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch returns',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST - Create return
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const body = await request.json()
    const { distribution_id, quantity, reason, condition, action, notes, production_info } = body

    console.log('🔄 Processing return for distribution:', distribution_id)

    if (!distribution_id || !quantity || !reason || !condition || !action) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Get distribution info
    const distributionResponse = await fetch(`${DIRECTUS_URL}/items/production_distributions/${distribution_id}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!distributionResponse.ok) {
      return NextResponse.json(
        { success: false, error: 'Distribution not found' },
        { status: 404 }
      )
    }

    const distributionData = await distributionResponse.json()
    const distribution = distributionData.data

    // Validate return quantity
    const maxReturnQuantity = distribution.quantity - (distribution.returned_quantity || 0)
    if (quantity > maxReturnQuantity) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Cannot return ${quantity} items. Maximum returnable: ${maxReturnQuantity}` 
        },
        { status: 400 }
      )
    }

    // Create return record
    const returnData = {
      distribution_id,
      produk_id: distribution.produk_id,
      quantity,
      reason,
      condition,
      action,
      returned_at: new Date().toISOString(),
      notes: notes || null,
      // Add production information if available
      production_id: production_info?.production_id || null,
      production_date: production_info?.production_date || null,
      days_from_production: production_info?.days_from_production || null
    }

    const createReturnResponse = await fetch(`${DIRECTUS_URL}/items/production_returns`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(returnData)
    })

    if (!createReturnResponse.ok) {
      const errorData = await createReturnResponse.json()
      throw new Error(`Failed to create return: ${errorData.errors?.[0]?.message || 'Unknown error'}`)
    }

    const createdReturn = await createReturnResponse.json()

    // Update distribution with return info
    const newReturnedQuantity = (distribution.returned_quantity || 0) + quantity
    const newStatus = newReturnedQuantity >= distribution.quantity ? 'returned' : 'partial'

    const updateDistributionData = {
      returned_quantity: newReturnedQuantity,
      status: newStatus,
      updated_at: new Date().toISOString()
    }

    const updateDistributionResponse = await fetch(`${DIRECTUS_URL}/items/production_distributions/${distribution_id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateDistributionData)
    })

    if (!updateDistributionResponse.ok) {
      console.error('⚠️ Failed to update distribution, but return was created')
    }

    // If action is restock and condition is good, add back to production stock
    if (action === 'restock' && condition === 'good') {
      try {
        // Get current stock
        const stockResponse = await fetch(`${DIRECTUS_URL}/items/production_stock?filter[produk_id][_eq]=${distribution.produk_id}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        })

        if (stockResponse.ok) {
          const stockData = await stockResponse.json()
          const stock = stockData.data?.[0]

          if (stock) {
            // Update stock quantities
            const updatedStock = {
              quantity: stock.quantity + quantity,
              reserved_quantity: Math.max(0, stock.reserved_quantity - quantity),
              available_quantity: stock.available_quantity + quantity,
              last_updated: new Date().toISOString()
            }

            const updateStockResponse = await fetch(`${DIRECTUS_URL}/items/production_stock/${stock.id}`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify(updatedStock)
            })

            if (updateStockResponse.ok) {
              console.log('✅ Stock updated successfully for restock action')
            } else {
              console.error('⚠️ Failed to update stock for restock action')
            }
          } else {
            // Create new stock record if none exists
            const newStock = {
              produk_id: distribution.produk_id,
              quantity,
              reserved_quantity: 0,
              available_quantity: quantity,
              last_updated: new Date().toISOString()
            }

            const createStockResponse = await fetch(`${DIRECTUS_URL}/items/production_stock`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify(newStock)
            })

            if (createStockResponse.ok) {
              console.log('✅ New stock record created for restock action')
            } else {
              console.error('⚠️ Failed to create stock record for restock action')
            }
          }
        }
      } catch (stockError) {
        console.error('⚠️ Error handling restock action:', stockError)
      }
    }

    console.log('✅ Return processed successfully')

    return NextResponse.json({
      success: true,
      data: createdReturn.data,
      message: 'Return processed successfully'
    })

  } catch (error) {
    console.error('❌ Error processing return:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process return',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
