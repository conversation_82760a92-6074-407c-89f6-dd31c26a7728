import { getProduk } from "@/lib/directus"
import { ProductionForm } from "../production-form"

export default async function AddProductionPage({
  searchParams,
}: {
  searchParams: Promise<{ product?: string }>
}) {
  const products = await getProduk()
  const params = await searchParams

  return (
    <ProductionForm
      products={products}
      initialProductId={params.product}
      mode="add"
    />
  )
}

