"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { ArrowLeft, Calendar, Package, TrendingUp, TrendingDown, AlertTriangle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { getProduk, getDailyProduction } from "@/lib/directus"
import { getDistributions, getProductionStock, getDistributionChannels } from "@/lib/production-api"
import { format } from "date-fns"

export default function SalesTrackingPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [products, setProducts] = useState<any[]>([])
  const [productionData, setProductionData] = useState<any[]>([])
  const [distributionData, setDistributionData] = useState<any[]>([])
  const [productionStock, setProductionStock] = useState<any[]>([])
  const [distributionChannels, setDistributionChannels] = useState<any[]>([])
  const [salesTrackingData, setSalesTrackingData] = useState<any[]>([])
  const [dateFilter, setDateFilter] = useState("")
  const [productFilter, setProductFilter] = useState("")
  const { toast } = useToast()

  // Helper function to safely format dates
  const safeFormatDate = (dateValue: any, formatString: string, fallback: string = 'N/A') => {
    if (!dateValue) return fallback
    try {
      const date = new Date(dateValue)
      if (isNaN(date.getTime())) return fallback
      return format(date, formatString)
    } catch (error) {
      console.warn('Invalid date value:', dateValue)
      return fallback
    }
  }

  useEffect(() => {
    async function fetchData() {
      console.log('Starting to fetch sales tracking data...')
      setIsLoading(true)
      try {
        // Fetch all historical data for sales tracking
        console.log('Fetching data from APIs...')

        const productsData = await getProduk()
        console.log('Products fetched:', productsData.length)

        const productionDataResult = await getDailyProduction({})
        console.log('Production data fetched:', productionDataResult.length)

        const stockData = await getProductionStock()
        console.log('Stock data fetched:', stockData.length)

        const distributionsData = await getDistributions()
        console.log('Distribution data fetched:', distributionsData.length)

        const channelsData = await getDistributionChannels()
        console.log('Channels data fetched:', channelsData.length)

        setProducts(productsData)
        setProductionData(productionDataResult)
        setProductionStock(stockData)
        setDistributionData(distributionsData)
        setDistributionChannels(channelsData)

        // Process sales tracking data using the same logic as production page
        const trackingData = processSalesTrackingData(productsData, productionDataResult, stockData, distributionsData)
        console.log('Final tracking data:', trackingData.length, 'items')
        setSalesTrackingData(trackingData)
      } catch (error) {
        console.error("Error fetching sales tracking data:", error)
        console.error("Error details:", error instanceof Error ? error.message : 'Unknown error')
        toast({
          title: "Error",
          description: `Failed to load sales tracking data: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [toast])

  const processSalesTrackingData = (products: any[], productions: any[], stock: any[], distributions: any[]) => {
    console.log('Processing sales tracking data with:', {
      products: products.length,
      productions: productions.length,
      stock: stock.length,
      distributions: distributions.length
    })

    const trackingMap = new Map()

    // If no production data, return empty
    if (productions.length === 0) {
      console.log('No production data available')
      return []
    }

    // Start with all production data to create tracking entries
    productions.forEach(production => {
      const dateKey = production.date || production.created_at.split('T')[0]
      const productId = production.produk_id
      const key = `${dateKey}-${productId}`

      if (!trackingMap.has(key)) {
        const product = products.find(p => p.id === productId)
        trackingMap.set(key, {
          production_date: dateKey,
          production_datetime: production.date || production.created_at,
          produk_id: productId,
          product_name: product?.nama_produk || 'Unknown Product',
          quantity_produced: 0,
          quantity_distributed: 0,
          quantity_returned: 0,
          quantity_remaining: 0,
          current_stock: 0,
          reserved_stock: 0,
          distributions: [],
          returns: [],
          status: 'pending'
        })
      }

      const item = trackingMap.get(key)
      item.quantity_produced += production.quantity_produced
    })

    console.log('Created tracking entries:', trackingMap.size)

    // Add current stock information
    stock.forEach(stockItem => {
      // Find the most recent production for this product
      const productProductions = productions.filter(p => p.produk_id === stockItem.produk_id)

      if (productProductions.length > 0) {
        const latestProduction = productProductions.reduce((latest, current) => {
          const currentDate = new Date(current.date || current.created_at)
          const latestDate = new Date(latest.date || latest.created_at)
          return currentDate > latestDate ? current : latest
        })

        const dateKey = latestProduction.date || latestProduction.created_at.split('T')[0]
        const key = `${dateKey}-${stockItem.produk_id}`

        if (trackingMap.has(key)) {
          const item = trackingMap.get(key)
          item.current_stock = stockItem.quantity
          item.reserved_stock = stockItem.reserved_quantity
          item.quantity_remaining = stockItem.available_quantity
        }
      }
    })

    // Add distribution data
    distributions.forEach(distribution => {
      const productId = distribution.produk_id

      // Find matching tracking item for this product
      for (const [key, item] of trackingMap.entries()) {
        if (item.produk_id === productId) {
          item.quantity_distributed += distribution.quantity
          item.quantity_returned += distribution.returned_quantity || 0
          item.distributions.push(distribution)
          break
        }
      }
    })

    // Calculate status based on current stock and production date
    trackingMap.forEach((item, key) => {
      // Use the actual remaining stock from production_stock table
      const actualRemaining = item.quantity_remaining

      if (actualRemaining <= 0) {
        item.status = 'sold_out'
      } else {
        const productionDate = new Date(item.production_datetime)
        const daysSinceProduction = Math.floor((new Date().getTime() - productionDate.getTime()) / (1000 * 60 * 60 * 24))

        if (daysSinceProduction >= 7) {
          item.status = 'expired'
        } else if (daysSinceProduction >= 5) {
          item.status = 'near_expiry'
        } else if (daysSinceProduction >= 3) {
          item.status = 'monitor'
        } else {
          item.status = 'fresh'
        }
      }

      item.days_since_production = Math.floor((new Date().getTime() - new Date(item.production_datetime).getTime()) / (1000 * 60 * 60 * 24))
    })

    return Array.from(trackingMap.values()).sort((a, b) => 
      new Date(b.production_datetime).getTime() - new Date(a.production_datetime).getTime()
    )
  }

  // Filter data based on date and product filters
  const filteredData = salesTrackingData.filter(item => {
    const matchesDate = !dateFilter || item.production_date.includes(dateFilter)
    const matchesProduct = !productFilter || item.product_name.toLowerCase().includes(productFilter.toLowerCase())
    return matchesDate && matchesProduct
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'sold_out':
        return <Badge variant="default" className="bg-green-50 text-green-700 border-green-200">Sold Out</Badge>
      case 'expired':
        return <Badge variant="destructive">Expired</Badge>
      case 'near_expiry':
        return <Badge variant="secondary" className="bg-orange-50 text-orange-700 border-orange-200">Near Expiry</Badge>
      case 'monitor':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Monitor</Badge>
      case 'fresh':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Fresh</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading sales tracking data...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="/production">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Production
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Sales Tracking by Production Date</h1>
          <p className="text-muted-foreground">
            Track sales performance and remaining stock for each production batch
          </p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {filteredData.filter(item => item.status === 'sold_out').length}
              </div>
              <p className="text-sm text-muted-foreground">Sold Out Batches</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {filteredData.filter(item => item.status === 'expired').length}
              </div>
              <p className="text-sm text-muted-foreground">Expired Batches</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {filteredData.filter(item => item.status === 'near_expiry').length}
              </div>
              <p className="text-sm text-muted-foreground">Near Expiry</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {filteredData.reduce((sum, item) => sum + item.quantity_remaining, 0)}
              </div>
              <p className="text-sm text-muted-foreground">Total Remaining</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="date-filter">Production Date</Label>
              <Input
                id="date-filter"
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                placeholder="Filter by production date"
              />
            </div>
            <div>
              <Label htmlFor="product-filter">Product Name</Label>
              <Input
                id="product-filter"
                value={productFilter}
                onChange={(e) => setProductFilter(e.target.value)}
                placeholder="Filter by product name"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sales Tracking Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Sales Tracking by Production Date
          </CardTitle>
          <CardDescription>
            Monitor sales performance and remaining stock for each production batch
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredData.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-muted-foreground">No production data found</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Production Date</TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Produced</TableHead>
                  <TableHead>Distributed</TableHead>
                  <TableHead>Returned</TableHead>
                  <TableHead>Remaining</TableHead>
                  <TableHead>Days Since Production</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Sales Rate</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((item, index) => {
                  const salesRate = item.quantity_produced > 0 
                    ? Math.round((item.quantity_distributed / item.quantity_produced) * 100) 
                    : 0
                  
                  return (
                    <TableRow key={index}>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {safeFormatDate(item.production_datetime, "dd/MM/yyyy")}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {safeFormatDate(item.production_datetime, "HH:mm")}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{item.product_name}</TableCell>
                      <TableCell>{item.quantity_produced}</TableCell>
                      <TableCell>{item.quantity_distributed}</TableCell>
                      <TableCell>{item.quantity_returned}</TableCell>
                      <TableCell>
                        <span className={`font-medium ${
                          item.quantity_remaining <= 0 ? 'text-green-600' :
                          item.status === 'expired' ? 'text-red-600' :
                          item.status === 'near_expiry' ? 'text-orange-600' : 'text-blue-600'
                        }`}>
                          {item.quantity_remaining}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className={`font-medium ${
                          item.days_since_production >= 7 ? 'text-red-600' :
                          item.days_since_production >= 5 ? 'text-orange-600' :
                          item.days_since_production >= 3 ? 'text-yellow-600' : 'text-green-600'
                        }`}>
                          {item.days_since_production} days
                        </span>
                      </TableCell>
                      <TableCell>{getStatusBadge(item.status)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className={`font-medium ${
                            salesRate >= 90 ? 'text-green-600' :
                            salesRate >= 70 ? 'text-blue-600' :
                            salesRate >= 50 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {salesRate}%
                          </span>
                          {salesRate >= 90 ? <TrendingUp className="h-4 w-4 text-green-600" /> :
                           salesRate < 50 ? <TrendingDown className="h-4 w-4 text-red-600" /> :
                           <AlertTriangle className="h-4 w-4 text-yellow-600" />}
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
