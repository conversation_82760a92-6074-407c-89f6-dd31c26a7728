"use client"

import { useState, useEffect, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft, RotateCcw, AlertTriangle, CheckCircle, XCircle } from "lucide-react"
import { getProduk, getDailyProduction } from "@/lib/directus"
import { getDistributions, createReturn } from "@/lib/production-api"
import type { Produk } from "@/lib/directus"
import type { ProductionDistribution, ReturnFormData } from "@/lib/production-types"

function AddReturnContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [products, setProducts] = useState<Produk[]>([])
  const [distributions, setDistributions] = useState<ProductionDistribution[]>([])
  const [selectedDistribution, setSelectedDistribution] = useState<ProductionDistribution | null>(null)
  const [productionData, setProductionData] = useState<any[]>([])
  const [selectedProductionInfo, setSelectedProductionInfo] = useState<any>(null)
  
  const [formData, setFormData] = useState<ReturnFormData>({
    distribution_id: "",
    quantity: 0,
    reason: "",
    condition: "good",
    action: "restock",
    notes: ""
  })

  // Helper function to find production info for a distribution
  const findProductionInfoForDistribution = (distribution: ProductionDistribution, productions: any[]) => {
    if (!distribution || !productions.length) return null

    // Find production records for this product
    const productProductions = productions.filter(p => p.produk_id === distribution.produk_id)

    if (productProductions.length === 0) return null

    // Find the production record closest to the distribution date
    const distributionDate = new Date(distribution.date)
    let closestProduction = null
    let minDiff = Infinity

    for (const prod of productProductions) {
      const prodDate = new Date(prod.date || prod.created_at)
      const diff = Math.abs(distributionDate.getTime() - prodDate.getTime())

      // Only consider productions that happened before or on the distribution date
      if (prodDate <= distributionDate && diff < minDiff) {
        minDiff = diff
        closestProduction = prod
      }
    }

    if (closestProduction) {
      const prodDate = new Date(closestProduction.date || closestProduction.created_at)
      const daysBetween = Math.floor((distributionDate.getTime() - prodDate.getTime()) / (1000 * 60 * 60 * 24))

      return {
        ...closestProduction,
        production_date: prodDate,
        days_to_distribution: daysBetween
      }
    }

    return null
  }

  useEffect(() => {
    async function fetchData() {
      try {
        const distributionId = searchParams.get('distribution')

        const [productsData, distributionsData, productionDataResult] = await Promise.all([
          getProduk(),
          getDistributions(undefined, undefined, 'distributed'), // Only distributed items can be returned
          getDailyProduction({}) // Get all production data
        ])

        setProducts(productsData)
        setDistributions(distributionsData)
        setProductionData(productionDataResult)

        console.log('📊 Returns page data loaded:')
        console.log('Products:', productsData?.length || 0)
        console.log('Distributions (distributed only):', distributionsData?.length || 0, distributionsData)
        console.log('Production data:', productionDataResult?.length || 0)

        // Pre-select distribution if provided in URL
        if (distributionId) {
          const distribution = distributionsData.find(d => d.id === distributionId)
          if (distribution) {
            setSelectedDistribution(distribution)
            setFormData(prev => ({ ...prev, distribution_id: distributionId }))

            // Find production info for this distribution
            const productionInfo = findProductionInfoForDistribution(distribution, productionDataResult)
            setSelectedProductionInfo(productionInfo)
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to load data",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [searchParams, toast])

  const handleDistributionChange = (distributionId: string) => {
    const distribution = distributions.find(d => d.id === distributionId)
    setSelectedDistribution(distribution || null)
    setFormData(prev => ({
      ...prev,
      distribution_id: distributionId,
      quantity: 0 // Reset quantity when distribution changes
    }))

    // Find and set production info for this distribution
    if (distribution) {
      const productionInfo = findProductionInfoForDistribution(distribution, productionData)
      setSelectedProductionInfo(productionInfo)
    } else {
      setSelectedProductionInfo(null)
    }
  }

  const getMaxReturnQuantity = () => {
    if (!selectedDistribution) return 0
    return selectedDistribution.quantity - (selectedDistribution.returned_quantity || 0)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!formData.distribution_id) {
      toast({
        title: "Validation Error",
        description: "Please select a distribution",
        variant: "destructive"
      })
      return
    }

    if (formData.quantity <= 0) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid quantity",
        variant: "destructive"
      })
      return
    }

    if (formData.quantity > getMaxReturnQuantity()) {
      toast({
        title: "Validation Error",
        description: `Maximum return quantity is ${getMaxReturnQuantity()}`,
        variant: "destructive"
      })
      return
    }

    if (!formData.reason.trim()) {
      toast({
        title: "Validation Error",
        description: "Please provide a reason for the return",
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Enhance form data with production information
      const enhancedFormData = {
        ...formData,
        production_info: selectedProductionInfo ? {
          production_id: selectedProductionInfo.id,
          production_date: selectedProductionInfo.production_date.toISOString(),
          days_from_production: selectedProductionInfo.days_to_distribution
        } : null
      }

      await createReturn(enhancedFormData)

      toast({
        title: "Success",
        description: "Return processed successfully",
      })

      router.push('/production?tab=returns')
    } catch (error) {
      console.error("Error processing return:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process return",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Process Return</h1>
          <p className="text-muted-foreground">
            Handle returned products from distribution channels
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <RotateCcw className="h-5 w-5" />
              <span>Return Information</span>
            </CardTitle>
            <CardDescription>
              Select the distribution and specify return details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Distribution Selection */}
            <div className="space-y-2">
              <Label>Distribution *</Label>
              <Select
                value={formData.distribution_id}
                onValueChange={handleDistributionChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select distribution to return from" />
                </SelectTrigger>
                <SelectContent>
                  {distributions.map((distribution) => {
                    // Try to get product name from relation first
                    const productName = (distribution as any).produk_id?.nama_produk ||
                      products.find(p => p.id === distribution.produk_id)?.nama_produk ||
                      `Product ${distribution.produk_id}`
                    const maxReturn = distribution.quantity - (distribution.returned_quantity || 0)
                    return (
                      <SelectItem key={distribution.id} value={distribution.id}>
                        {productName} - {distribution.quantity} units
                        (Can return: {maxReturn})
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Distribution Details */}
            {selectedDistribution && (
              <div className="p-4 bg-muted rounded-lg space-y-4">
                <h4 className="font-medium">Distribution Details</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Product:</span>
                    <p className="font-medium">
                      {(selectedDistribution as any).produk_id?.nama_produk ||
                       products.find(p => p.id === selectedDistribution.produk_id)?.nama_produk ||
                       `Product ${selectedDistribution.produk_id}`}
                    </p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Channel:</span>
                    <p className="font-medium">
                      {(selectedDistribution as any).distribution_channel_id?.name || selectedDistribution.distribution_channel_id}
                    </p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Distribution Date:</span>
                    <p className="font-medium">
                      {new Date(selectedDistribution.date).toLocaleDateString('id-ID', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric'
                      })}
                    </p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Distributed:</span>
                    <p className="font-medium">{selectedDistribution.quantity} units</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Already Returned:</span>
                    <p className="font-medium">{selectedDistribution.returned_quantity || 0} units</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Available to Return:</span>
                    <p className="font-medium text-green-600">{getMaxReturnQuantity()} units</p>
                  </div>
                </div>

                {/* Production Information */}
                {selectedProductionInfo && (
                  <div className="border-t pt-4">
                    <h5 className="font-medium mb-2 text-blue-600">Production Information</h5>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Production Date:</span>
                        <p className="font-medium">
                          {selectedProductionInfo.production_date.toLocaleDateString('id-ID', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric'
                          })} {selectedProductionInfo.production_date.toLocaleTimeString('id-ID', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Days to Distribution:</span>
                        <p className="font-medium">
                          {selectedProductionInfo.days_to_distribution} days
                        </p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Quantity Produced:</span>
                        <p className="font-medium">{selectedProductionInfo.quantity_produced} units</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Production Batch:</span>
                        <p className="font-medium text-xs text-muted-foreground">
                          ID: {selectedProductionInfo.id}
                        </p>
                      </div>
                    </div>

                    {/* Age indicator */}
                    <div className="mt-2">
                      <span className="text-muted-foreground">Product Age at Return:</span>
                      <p className={`font-medium ${
                        selectedProductionInfo.days_to_distribution >= 7 ? 'text-red-600' :
                        selectedProductionInfo.days_to_distribution >= 5 ? 'text-orange-600' :
                        selectedProductionInfo.days_to_distribution >= 3 ? 'text-yellow-600' : 'text-green-600'
                      }`}>
                        {selectedProductionInfo.days_to_distribution} days from production
                        {selectedProductionInfo.days_to_distribution >= 7 && ' (Expired)'}
                        {selectedProductionInfo.days_to_distribution >= 5 && selectedProductionInfo.days_to_distribution < 7 && ' (Near Expiry)'}
                        {selectedProductionInfo.days_to_distribution >= 3 && selectedProductionInfo.days_to_distribution < 5 && ' (Monitor)'}
                        {selectedProductionInfo.days_to_distribution < 3 && ' (Fresh)'}
                      </p>
                    </div>
                  </div>
                )}

                {!selectedProductionInfo && (
                  <div className="border-t pt-4">
                    <p className="text-sm text-muted-foreground">
                      ⚠️ Production information not found for this distribution
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Return Quantity */}
            <div className="space-y-2">
              <Label>Return Quantity *</Label>
              <Input
                type="number"
                min="1"
                max={getMaxReturnQuantity()}
                value={formData.quantity}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  quantity: parseInt(e.target.value) || 0 
                }))}
                placeholder="0"
              />
              <p className="text-sm text-muted-foreground">
                Maximum: {getMaxReturnQuantity()} units
              </p>
            </div>

            {/* Return Reason */}
            <div className="space-y-2">
              <Label>Return Reason *</Label>
              <Textarea
                value={formData.reason}
                onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                placeholder="Why are these items being returned?"
                rows={3}
              />
            </div>

            {/* Product Condition */}
            <div className="space-y-3">
              <Label>Product Condition *</Label>
              <RadioGroup
                value={formData.condition}
                onValueChange={(value: 'good' | 'damaged' | 'expired') => 
                  setFormData(prev => ({ ...prev, condition: value }))
                }
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="good" id="good" />
                  <Label htmlFor="good" className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Good - Can be resold</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="damaged" id="damaged" />
                  <Label htmlFor="damaged" className="flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    <span>Damaged - Needs assessment</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="expired" id="expired" />
                  <Label htmlFor="expired" className="flex items-center space-x-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span>Expired - Cannot be resold</span>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Action to Take */}
            <div className="space-y-3">
              <Label>Action to Take *</Label>
              <RadioGroup
                value={formData.action}
                onValueChange={(value: 'restock' | 'dispose' | 'redistribute') => 
                  setFormData(prev => ({ ...prev, action: value }))
                }
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="restock" id="restock" />
                  <Label htmlFor="restock">Restock - Add back to available inventory</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="dispose" id="dispose" />
                  <Label htmlFor="dispose">Dispose - Remove from inventory</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="redistribute" id="redistribute" />
                  <Label htmlFor="redistribute">Redistribute - Hold for redistribution</Label>
                </div>
              </RadioGroup>
            </div>

            {/* Additional Notes */}
            <div className="space-y-2">
              <Label>Additional Notes</Label>
              <Textarea
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Any additional information about this return..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting || !selectedDistribution}>
            {isSubmitting ? "Processing..." : "Process Return"}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default function AddReturnPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    }>
      <AddReturnContent />
    </Suspense>
  )
}
