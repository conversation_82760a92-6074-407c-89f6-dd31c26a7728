"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft, Plus, Trash2, Truck } from "lucide-react"
import type { Produk } from "@/lib/directus"
import type { ProductionStock, DistributionChannel } from "@/lib/production-types"

interface DistributionItem {
  id: string
  produk_id: string
  channel_id: string
  quantity: number
  notes: string
}

export default function AddDistributionPage() {
  const router = useRouter()
  const { toast } = useToast()
  
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [products, setProducts] = useState<Produk[]>([])
  const [productionStock, setProductionStock] = useState<ProductionStock[]>([])
  const [distributionChannels, setDistributionChannels] = useState<DistributionChannel[]>([])
  const [distributions, setDistributions] = useState<DistributionItem[]>([
    {
      id: crypto.randomUUID(),
      produk_id: "",
      channel_id: "",
      quantity: 0,
      notes: ""
    }
  ])

  useEffect(() => {
    async function fetchData() {
      try {
        const token = localStorage.getItem('directus_token')
        if (!token) {
          throw new Error('Authentication token not found')
        }

        // Fetch products
        const productsResponse = await fetch('/api/production/products', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
        if (!productsResponse.ok) throw new Error('Failed to fetch products')
        const productsResult = await productsResponse.json()
        const productsData = productsResult.success ? productsResult.data : []

        // Fetch production stock
        const stockResponse = await fetch('/api/production/stock', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
        if (!stockResponse.ok) throw new Error('Failed to fetch stock')
        const stockResult = await stockResponse.json()
        const stockData = stockResult.success ? stockResult.data : []

        // Fetch distribution channels
        const channelsResponse = await fetch('/api/production/channels', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
        if (!channelsResponse.ok) throw new Error('Failed to fetch channels')
        const channelsResult = await channelsResponse.json()
        const channelsData = channelsResult.success ? channelsResult.data : []

        setProducts(productsData)
        setProductionStock(stockData)
        setDistributionChannels(channelsData)

        console.log('📊 Loaded data:')
        console.log('Products:', productsData?.length || 0)
        console.log('Production Stock:', stockData?.length || 0)
        console.log('Distribution Channels:', channelsData?.length || 0)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to load data",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [toast])

  const addDistributionItem = () => {
    setDistributions(prev => [...prev, {
      id: crypto.randomUUID(),
      produk_id: "",
      channel_id: "",
      quantity: 0,
      notes: ""
    }])
  }

  const removeDistributionItem = (id: string) => {
    if (distributions.length > 1) {
      setDistributions(prev => prev.filter(item => item.id !== id))
    }
  }

  const updateDistributionItem = (id: string, field: keyof DistributionItem, value: any) => {
    setDistributions(prev => prev.map(item =>
      item.id === id ? { ...item, [field]: value } : item
    ))
  }

  const getAvailableStock = (produk_id: string) => {
    // If no production stock data is available, return 0
    if (productionStock.length === 0) {
      return 0
    }

    // Find stock for this product with flexible matching
    const stock = productionStock.find(s => {
      const stockId = s.produk_id
      const searchId = produk_id

      // Try exact match first
      if (stockId === searchId) return true

      // Try string comparison
      if (stockId?.toString() === searchId?.toString()) return true

      // Try number comparison if both can be converted
      try {
        const stockIdNum = parseInt(stockId?.toString() || '')
        const searchIdNum = parseInt(searchId?.toString() || '')
        if (!isNaN(stockIdNum) && !isNaN(searchIdNum) && stockIdNum === searchIdNum) return true
      } catch (e) {
        // Ignore conversion errors
      }

      return false
    })

    if (!stock) {
      return 0 // Return 0 when no stock record exists
    }

    return stock.available_quantity || 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    e.stopPropagation()

    console.log('🚀 Form submitted, current distributions:', distributions)
    console.log('🚀 Event type:', e.type)
    console.log('🚀 Is submitting:', isSubmitting)

    // Check if data is still loading
    if (isLoading) {
      console.log('⚠️ Data is still loading, preventing submission')
      toast({
        title: "Please Wait",
        description: "Data is still loading. Please wait a moment.",
        variant: "destructive"
      })
      return
    }

    // Validation
    const validDistributions = distributions.filter(d => d.produk_id && d.channel_id && d.quantity > 0)

    console.log('✅ Valid distributions:', validDistributions)

    if (validDistributions.length === 0) {
      console.log('❌ No valid distributions found')
      toast({
        title: "Validation Error",
        description: "Please add at least one valid distribution",
        variant: "destructive"
      })
      return
    }

    console.log('🔍 Proceeding with validation checks...')

    // Check stock availability
    console.log('🔍 Checking stock availability...')
    console.log('📊 Production stock data available:', productionStock.length > 0)
    console.log('📊 Current production stock state:', productionStock)
    console.log('📊 Products state:', products.length)
    console.log('📊 Distribution channels state:', distributionChannels.length)

    // Validate stock for all distributions
    for (const dist of validDistributions) {
      const availableStock = getAvailableStock(dist.produk_id)
      if (dist.quantity > availableStock) {
        const product = products.find(p => p.id === dist.produk_id)
        toast({
          title: "Insufficient Stock",
          description: `Not enough stock for ${product?.nama_produk}. Available: ${availableStock}`,
          variant: "destructive"
        })
        return
      }
    }

    setIsSubmitting(true)

    try {
      // Group distributions by product
      const distributionsByProduct = validDistributions.reduce((acc, dist) => {
        if (!acc[dist.produk_id]) {
          acc[dist.produk_id] = []
        }
        acc[dist.produk_id].push({
          channel_id: dist.channel_id,
          quantity: dist.quantity,
          notes: dist.notes
        })
        return acc
      }, {} as Record<string, any[]>)

      // Create distributions for each product using API endpoint
      const results = []
      const entries = Object.entries(distributionsByProduct)

      for (const [produk_id, distributions] of entries) {
        const token = localStorage.getItem('directus_token')

        try {
          const response = await fetch('/api/production/distribution', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              produk_id,
              distributions
            })
          })

          const result = await response.json()

          if (!response.ok) {
            throw new Error(result.error || result.details || `HTTP ${response.status}: ${result.message || 'Failed to create distribution'}`)
          }

          if (result.success && result.data) {
            results.push(...result.data)
          } else {
            throw new Error('Unexpected response format from server')
          }
        } catch (fetchError) {
          throw fetchError
        }
      }

      toast({
        title: "Success",
        description: `Created ${results.length} distribution(s) successfully`,
      })

      router.push('/production?tab=distribution')
    } catch (error) {
      console.error("Error creating distributions:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create distributions",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Create Distribution</h1>
          <p className="text-muted-foreground">
            Distribute products to various channels
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Truck className="h-5 w-5" />
              <span>Distribution Items</span>
            </CardTitle>
            <CardDescription>
              Add products and quantities to distribute
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {distributions.map((distribution, index) => (
              <div
                key={distribution.id}
                className="border rounded-lg p-4 space-y-4"
              >
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Distribution #{index + 1}</h4>
                  {distributions.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeDistributionItem(distribution.id)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Product *</Label>
                    <Select
                      value={distribution.produk_id || undefined}
                      onValueChange={(value) => updateDistributionItem(distribution.id, 'produk_id', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select product" />
                      </SelectTrigger>
                      <SelectContent>
                        {products.length === 0 ? (
                          <SelectItem value="loading" disabled>
                            {isLoading ? "Loading products..." : "No products available"}
                          </SelectItem>
                        ) : (
                          products.map((product) => {
                            const availableStock = getAvailableStock(product.id)
                            return (
                              <SelectItem key={product.id} value={product.id}>
                                {product.nama_produk} (Available: {availableStock})
                              </SelectItem>
                            )
                          })
                        )}
                      </SelectContent>
                    </Select>
                    {distribution.produk_id && (
                      <div className="mt-2 p-2 bg-muted/50 rounded-md">
                        <p className="text-sm font-medium text-foreground">
                          ✅ Selected: {products.find(p => p.id === distribution.produk_id)?.nama_produk}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Distribution Channel *</Label>
                    <Select
                      value={distribution.channel_id || undefined}
                      onValueChange={(value) => updateDistributionItem(distribution.id, 'channel_id', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select channel" />
                      </SelectTrigger>
                      <SelectContent>
                        {distributionChannels.length === 0 ? (
                          <SelectItem value="loading" disabled>
                            {isLoading ? "Loading channels..." : "No channels available"}
                          </SelectItem>
                        ) : (
                          distributionChannels.map((channel) => (
                            <SelectItem key={channel.id} value={channel.id}>
                              {channel.name} ({channel.type})
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    {distribution.channel_id && (
                      <div className="mt-2 p-2 bg-muted/50 rounded-md">
                        <p className="text-sm font-medium text-foreground">
                          ✅ Selected: {distributionChannels.find(c => c.id === distribution.channel_id)?.name}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Quantity *</Label>
                    <Input
                      type="number"
                      min="1"
                      value={distribution.quantity}
                      onChange={(e) => updateDistributionItem(distribution.id, 'quantity', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                    {distribution.produk_id && (
                      <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-950 rounded-md">
                        <p className="text-sm font-medium text-blue-700 dark:text-blue-300">
                          📦 Available Stock: {getAvailableStock(distribution.produk_id)}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Notes</Label>
                    <Textarea
                      value={distribution.notes}
                      onChange={(e) => updateDistributionItem(distribution.id, 'notes', e.target.value)}
                      placeholder="Additional notes..."
                      rows={2}
                    />
                  </div>
                </div>
              </div>
            ))}

            <div className="flex justify-center pt-4">
              <Button
                type="button"
                onClick={addDistributionItem}
                variant="outline"
                size="sm"
                className="w-full max-w-xs"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Distribution Item
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Distributions"}
          </Button>
        </div>
      </form>
    </div>
  )
}
