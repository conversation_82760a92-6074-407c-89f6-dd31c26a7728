"use client"

import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { ArrowLeft, Calendar, Filter, Search, Eye, Edit, Truck } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { useRouter } from 'next/navigation'
import { useToast } from '@/components/ui/use-toast'
import { getProduk } from '@/lib/directus'
import { getDistributions, getDistributionChannels } from '@/lib/production-api'
import type { Produk } from '@/lib/directus'
import type { ProductionDistribution, DistributionChannel } from '@/lib/production-types'

export default function DistributionHistoryPage() {
  const router = useRouter()
  const { toast } = useToast()
  
  const [isLoading, setIsLoading] = useState(true)
  const [distributions, setDistributions] = useState<ProductionDistribution[]>([])
  const [products, setProducts] = useState<Produk[]>([])
  const [channels, setChannels] = useState<DistributionChannel[]>([])
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [channelFilter, setChannelFilter] = useState('')
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setIsLoading(true)
      
      const [distributionsData, productsData, channelsData] = await Promise.all([
        getDistributions(), // Get all distributions
        getProduk(),
        getDistributionChannels()
      ])

      setDistributions(distributionsData || [])
      setProducts(productsData || [])
      setChannels(channelsData || [])
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: "Error",
        description: "Failed to load distribution history",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getProductName = (distribution: any) => {
    // Try to get from relation first
    if (distribution.produk_id?.nama_produk) {
      return distribution.produk_id.nama_produk
    }
    // Fallback to finding in products array
    const product = products.find(p => p.id === distribution.produk_id)
    return product?.nama_produk || 'Unknown Product'
  }

  const getChannelName = (distribution: any) => {
    // Try to get from relation first
    if (distribution.distribution_channel_id?.name) {
      const channelType = distribution.distribution_channel_id?.type || ''
      return `${distribution.distribution_channel_id.name}${channelType ? ` (${channelType})` : ''}`
    }
    // Fallback to finding in channels array
    const channel = channels.find(c => c.id === distribution.distribution_channel_id)
    return channel ? `${channel.name} (${channel.type})` : 'Unknown Channel'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'distributed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredDistributions = distributions.filter(dist => {
    const productName = getProductName(dist.produk_id).toLowerCase()
    const channelName = getChannelName(dist.distribution_channel_id).toLowerCase()
    const searchMatch = searchTerm === '' || 
      productName.includes(searchTerm.toLowerCase()) ||
      channelName.includes(searchTerm.toLowerCase())
    
    const statusMatch = statusFilter === '' || dist.status === statusFilter
    const channelMatch = channelFilter === '' || dist.distribution_channel_id === channelFilter
    
    let dateMatch = true
    if (dateFrom || dateTo) {
      const distDate = new Date(dist.date)
      if (dateFrom) {
        dateMatch = dateMatch && distDate >= new Date(dateFrom)
      }
      if (dateTo) {
        dateMatch = dateMatch && distDate <= new Date(dateTo)
      }
    }
    
    return searchMatch && statusMatch && channelMatch && dateMatch
  })

  const clearFilters = () => {
    setSearchTerm('')
    setStatusFilter('')
    setChannelFilter('')
    setDateFrom('')
    setDateTo('')
  }

  const updateDistributionStatus = async (distributionId: string, newStatus: string) => {
    try {
      const token = localStorage.getItem('directus_token')
      
      const response = await fetch('/api/production/distribution', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          id: distributionId,
          status: newStatus
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update distribution status')
      }

      toast({
        title: "Success",
        description: "Distribution status updated successfully",
      })

      // Refresh data
      await fetchData()
    } catch (error) {
      console.error('Error updating distribution status:', error)
      toast({
        title: "Error",
        description: "Failed to update distribution status",
        variant: "destructive"
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Distribution History</h1>
          <p className="text-muted-foreground">
            Track and manage distribution records
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search products, channels..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All statuses</SelectItem>
                  <SelectItem value="planned">Planned</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="distributed">Distributed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Channel</Label>
              <Select value={channelFilter} onValueChange={setChannelFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All channels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All channels</SelectItem>
                  {channels.map((channel) => (
                    <SelectItem key={channel.id} value={channel.id}>
                      {channel.name} ({channel.type})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Date From</Label>
              <Input
                type="date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label>Date To</Label>
              <Input
                type="date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex justify-between items-center mt-4">
            <p className="text-sm text-muted-foreground">
              Showing {filteredDistributions.length} of {distributions.length} distributions
            </p>
            <Button variant="outline" onClick={clearFilters}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Distribution Table */}
      <Card>
        <CardHeader>
          <CardTitle>Distribution Records</CardTitle>
          <CardDescription>
            Complete history of product distributions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredDistributions.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-muted-foreground">No distributions found</p>
              <p className="text-sm text-muted-foreground mt-2">
                Try adjusting your filters or create a new distribution
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Channel</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDistributions.map((distribution) => (
                  <TableRow key={distribution.id}>
                    <TableCell>
                      {format(new Date(distribution.date), "dd/MM/yyyy")}
                    </TableCell>
                    <TableCell className="font-medium">
                      {getProductName(distribution)}
                    </TableCell>
                    <TableCell>
                      {getChannelName(distribution)}
                    </TableCell>
                    <TableCell>{distribution.quantity}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(distribution.status)}>
                        {distribution.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {distribution.notes || '-'}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        {distribution.status === 'planned' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateDistributionStatus(distribution.id, 'in_progress')}
                          >
                            <Truck className="h-4 w-4 mr-1" />
                            Start
                          </Button>
                        )}
                        {distribution.status === 'in_progress' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateDistributionStatus(distribution.id, 'distributed')}
                          >
                            <Truck className="h-4 w-4 mr-1" />
                            Complete
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
