"use server"

import directus from "@/lib/directus"
import { readItems, createItem, updateItem } from "@directus/sdk"
import type {
  ProductionStock,
  DistributionChannel,
  DailyProductionSummary,
  ProductionAnalytics,
  ProductionDashboardData,
  DistributionFormData,
  ReturnFormData
} from "./production-types"

// ===== PRODUCTION STOCK MANAGEMENT =====

export async function getProductionStock(produk_id?: string) {
  try {
    const filter = produk_id ? { produk_id: { _eq: produk_id } } : {}
    
    return await directus.request(
      readItems("production_stock", {
        filter,
        fields: ["*"]
      })
    )
  } catch (error) {
    console.error("Error fetching production stock:", error)
    throw error
  }
}

export async function updateProductionStock(id: string, data: Partial<ProductionStock>) {
  try {
    return await directus.request(
      updateItem("production_stock", id, {
        ...data,
        last_updated: new Date().toISOString()
      })
    )
  } catch (error) {
    console.error("Error updating production stock:", error)
    throw error
  }
}

export async function addProductionStock(produk_id: string, quantity: number, notes?: string) {
  try {
    // Get current stock
    const currentStock = await directus.request(
      readItems("production_stock", {
        filter: { produk_id: { _eq: produk_id } }
      })
    )

    if (currentStock.length > 0) {
      // Update existing stock
      const stock = currentStock[0]
      const newQuantity = stock.quantity + quantity
      const newAvailable = newQuantity - stock.reserved_quantity

      return await directus.request(
        updateItem("production_stock", stock.id, {
          quantity: newQuantity,
          available_quantity: newAvailable,
          last_updated: new Date().toISOString()
        })
      )
    } else {
      // Create new stock record
      return await directus.request(
        createItem("production_stock", {
          produk_id,
          quantity,
          reserved_quantity: 0,
          available_quantity: quantity,
          last_updated: new Date().toISOString()
        })
      )
    }
  } catch (error) {
    console.error("Error adding production stock:", error)
    throw error
  }
}

// ===== DISTRIBUTION CHANNELS =====

export async function getDistributionChannels(type?: 'kangider' | 'sekolah' | 'reseller') {
  try {
    // Use direct Directus call for better reliability
    const filter = type ? { type: { _eq: type } } : {}

    return await directus.request(
      readItems("distribution_channels", {
        filter: {
          ...filter,
          status: { _eq: 'active' }
        },
        sort: ["name"]
      })
    )
  } catch (error) {
    console.error("Error fetching distribution channels:", error)
    throw error
  }
}

export async function createDistributionChannel(data: Omit<DistributionChannel, "id" | "created_at">) {
  try {
    return await directus.request(
      createItem("distribution_channels", {
        ...data
      })
    )
  } catch (error) {
    console.error("Error creating distribution channel:", error)
    throw error
  }
}

// ===== PRODUCTION DISTRIBUTION =====

export async function createDistribution(data: DistributionFormData) {
  try {
    const results = []
    const today = new Date().toISOString().split('T')[0]

    for (const dist of data.distributions) {
      // Check available stock
      const stock = await directus.request(
        readItems("production_stock", {
          filter: { produk_id: { _eq: data.produk_id } }
        })
      )

      if (stock.length === 0 || stock[0].available_quantity < dist.quantity) {
        throw new Error(`Insufficient stock for product ${data.produk_id}`)
      }

      // Create distribution record
      const distribution = await directus.request(
        createItem("production_distributions", {
          produk_id: data.produk_id,
          distribution_channel_id: dist.channel_id,
          quantity: dist.quantity,
          date: today,
          status: 'planned',
          notes: dist.notes
        })
      )

      // Update stock (reserve quantity)
      await directus.request(
        updateItem("production_stock", stock[0].id, {
          reserved_quantity: stock[0].reserved_quantity + dist.quantity,
          available_quantity: stock[0].available_quantity - dist.quantity,
          last_updated: new Date().toISOString()
        })
      )

      results.push(distribution)
    }

    return results
  } catch (error) {
    console.error("Error creating distribution:", error)
    throw error
  }
}

export async function updateDistributionStatus(id: string, status: 'distributed' | 'completed', notes?: string) {
  try {
    return await directus.request(
      updateItem("production_distributions", id, {
        status,
        distributed_at: status === 'distributed' ? new Date().toISOString() : undefined,
        notes
      })
    )
  } catch (error) {
    console.error("Error updating distribution status:", error)
    throw error
  }
}

export async function getDistributions(date?: string, channel_id?: string, status?: string) {
  try {
    const filter: any = {}

    if (date) filter.date = { _eq: date }
    if (channel_id) filter.distribution_channel_id = { _eq: channel_id }
    if (status) filter.status = { _eq: status }

    return await directus.request(
      readItems("production_distributions", {
        filter,
        fields: ["*", "produk_id.nama_produk", "distribution_channel_id.name", "distribution_channel_id.type"],
        sort: ["-date"]
      })
    )
  } catch (error) {
    console.error("Error fetching distributions:", error)
    throw error
  }
}

// ===== PRODUCTION RETURNS =====

export async function createReturn(data: ReturnFormData) {
  try {
    // Get distribution info
    const distribution = await directus.request(
      readItems("production_distributions", {
        filter: { id: { _eq: data.distribution_id } },
        fields: ["*"]
      })
    )

    if (distribution.length === 0) {
      throw new Error("Distribution not found")
    }

    const dist = distribution[0]

    // Create return record
    const returnRecord = await directus.request(
      createItem("production_returns", {
        distribution_id: data.distribution_id,
        produk_id: dist.produk_id,
        quantity: data.quantity,
        reason: data.reason,
        condition: data.condition,
        action: data.action,
        returned_at: new Date().toISOString(),
        notes: data.notes
      })
    )

    // Update distribution with return info
    await directus.request(
      updateItem("production_distributions", data.distribution_id, {
        returned_quantity: (dist.returned_quantity || 0) + data.quantity,
        status: dist.quantity === ((dist.returned_quantity || 0) + data.quantity) ? 'returned' : 'partial'
      })
    )

    // If action is restock, add back to production stock
    if (data.action === 'restock' && data.condition === 'good') {
      await addProductionStock(dist.produk_id, data.quantity, `Return: ${data.reason}`)
    }

    return returnRecord
  } catch (error) {
    console.error("Error creating return:", error)
    throw error
  }
}

// ===== DASHBOARD DATA =====

export async function getProductionDashboardData(): Promise<ProductionDashboardData> {
  try {
    const today = new Date().toISOString().split('T')[0]
    
    // Get today's production summary
    const todaySummary = await getDailyProductionSummary(today)
    
    // Get week analytics (placeholder - implement based on needs)
    const weekAnalytics: ProductionAnalytics = {
      period: 'weekly',
      start_date: today,
      end_date: today,
      total_production: 0,
      total_distribution: 0,
      total_returns: 0,
      return_rate: 0,
      top_products: [],
      channel_performance: []
    }

    // Get month analytics (placeholder - implement based on needs)
    const monthAnalytics: ProductionAnalytics = {
      period: 'monthly',
      start_date: today,
      end_date: today,
      total_production: 0,
      total_distribution: 0,
      total_returns: 0,
      return_rate: 0,
      top_products: [],
      channel_performance: []
    }

    return {
      today: todaySummary,
      week: weekAnalytics,
      month: monthAnalytics,
      alerts: {
        low_stock: [],
        high_returns: [],
        production_targets: []
      }
    }
  } catch (error) {
    console.error("Error fetching dashboard data:", error)
    throw error
  }
}

export async function getDailyProductionSummary(date: string): Promise<DailyProductionSummary> {
  try {
    // This is a simplified version - implement full logic based on your needs
    return {
      date,
      total_produced: 0,
      total_distributed: 0,
      total_returned: 0,
      total_available: 0,
      products: []
    }
  } catch (error) {
    console.error("Error fetching daily production summary:", error)
    throw error
  }
}
