# Mekanisme Production Management System

## Overview Alur Kerja

Sistem Production Management mengikuti alur kerja yang terstruktur dari produksi hingga analisis:

```
PRODUCTION → STOCK → DISTRIBUTION → BAD STOCK → RETURNS → ANALYSIS
```

## 1. PRODUCTION (Produksi <PERSON>)

### Proses:
1. **Input Produksi**: Staff memasukkan data produksi harian di `/production/daily/add`
   - Pilih produk
   - Masukkan target quantity
   - Masukkan actual quantity yang diproduksi
   - Tambahkan catatan (opsional)

2. **Status Produksi**:
   - `active`: Produksi sedang berlangsung
   - `completed`: Produksi selesai
   - `archived`: Data lama yang diarsipkan

3. **Sync ke Stock**: Setelah produksi selesai, data harus di-sync ke production stock
   - Klik tombol "Sync to Stock" di dashboard
   - Sistem akan memindahkan quantity ke `production_stock` table

### Database Tables:
- `daily_production`: Record produksi harian
- `production_stock`: Stock yang tersedia untuk distribusi

---

## 2. STOCK MANAGEMENT

### Production Stock Structure:
```sql
production_stock {
  id: UUID
  produk_id: UUID (FK to produk)
  quantity: INTEGER (total stock)
  reserved_quantity: INTEGER (stock yang sudah dialokasikan)
  available_quantity: INTEGER (stock yang bisa didistribusi)
  last_updated: TIMESTAMP
}
```

### Kalkulasi Stock:
- `available_quantity = quantity - reserved_quantity`
- Ketika distribusi dibuat: `reserved_quantity` bertambah
- Ketika distribusi di-distribute: stock tidak berubah (sudah reserved)
- Ketika ada return dengan action "restock": `quantity` bertambah

---

## 3. DISTRIBUTION (Distribusi ke Channel)

### Proses:
1. **Buat Distribusi**: `/production/distribution/add`
   - Pilih produk
   - Pilih distribution channel (kangider/sekolah/reseller)
   - Masukkan quantity (tidak boleh melebihi available stock)
   - Tambahkan notes

2. **Status Distribusi**:
   - `planned`: Distribusi direncanakan (stock sudah reserved)
   - `distributed`: Produk sudah dikirim ke channel
   - `returned`: Produk dikembalikan (sebagian/seluruh)
   - `completed`: Distribusi selesai

3. **Actions**:
   - **Distribute**: Ubah status dari `planned` ke `distributed`
   - **Return**: Proses pengembalian produk

### Distribution Channels:
- **Kangider**: Penjual keliling
- **Sekolah**: Kantin sekolah
- **Reseller**: Toko/warung

---

## 4. BAD STOCK (H+3 Rule)

### Konsep:
Produk yang sudah diproduksi lebih dari 3 hari (H+3) dianggap "bad stock" karena:
- Kualitas menurun
- Risiko kerusakan
- Perlu tindakan khusus

### Monitoring:
- Dashboard menampilkan bad stock alerts
- Kalkulasi: `production_date + 3 days < current_date`
- Prioritas: Produk dengan umur terlama ditampilkan pertama

### Tindakan untuk Bad Stock:
1. **Distribusi Prioritas**: Kirim ke channel yang bisa menjual cepat
2. **Discount**: Jual dengan harga diskon
3. **Return/Dispose**: Buang jika sudah tidak layak

---

## 5. RETURNS (Pengembalian)

### Proses:
1. **Akses**: `/production/returns/add`
2. **Pilih Distribusi**: Hanya distribusi dengan status `distributed` yang bisa di-return
3. **Return Details**:
   - **Quantity**: Jumlah yang dikembalikan
   - **Reason**: Alasan return (tidak laku, rusak, expired, dll)
   - **Condition**: 
     - `good`: Masih bagus
     - `damaged`: Rusak
     - `expired`: Kadaluarsa
   - **Action**:
     - `restock`: Kembalikan ke stock (hanya untuk condition: good)
     - `dispose`: Buang
     - `redistribute`: Kirim ke channel lain

### Impact ke Stock:
- **Restock + Good**: Tambah ke `production_stock.quantity`
- **Dispose/Redistribute**: Tidak mempengaruhi stock

---

## 6. ANALYSIS & REPORTING

### Dashboard Analytics:
1. **Production Analytics**:
   - Total produksi per periode
   - Target vs actual
   - Efficiency rate

2. **Distribution Analytics**:
   - Total distribusi per channel
   - Channel performance
   - Distribution rate

3. **Return Analytics**:
   - Return rate per channel
   - Return reasons
   - Loss analysis

4. **Stock Analytics**:
   - Current stock levels
   - Bad stock alerts
   - Stock turnover

### Key Metrics:
- **Production Efficiency**: `(actual_quantity / target_quantity) * 100`
- **Distribution Rate**: `distributed_quantity / produced_quantity * 100`
- **Return Rate**: `returned_quantity / distributed_quantity * 100`
- **Channel Performance**: Ranking berdasarkan volume dan return rate

---

## 7. WORKFLOW EXAMPLE

### Skenario Lengkap:

1. **Hari 1 - Produksi**:
   - Target: 100 cup kopi
   - Actual: 95 cup
   - Sync to stock: +95 available

2. **Hari 1 - Distribusi**:
   - Kangider A: 30 cup (planned)
   - Sekolah B: 40 cup (planned)
   - Available stock: 95 - 70 = 25 cup

3. **Hari 2 - Distribute**:
   - Kangider A: 30 cup (distributed)
   - Sekolah B: 40 cup (distributed)

4. **Hari 3 - Returns**:
   - Kangider A return: 5 cup (tidak laku, good condition, restock)
   - Available stock: 25 + 5 = 30 cup

5. **Hari 4 - Bad Stock Alert**:
   - 30 cup dari hari 1 sudah H+3
   - Perlu tindakan prioritas

### Stock Movement Tracking:
```
Initial: 0
+ Production: 95
- Reserved (Kangider): 30
- Reserved (Sekolah): 40
= Available: 25
+ Return (restock): 5
= Final Available: 30
```

---

## 8. API ENDPOINTS

### Production:
- `GET /api/production/daily` - List produksi
- `POST /api/production/daily` - Buat produksi
- `POST /api/production/daily/sync` - Sync ke stock

### Distribution:
- `GET /api/production/distribution` - List distribusi
- `POST /api/production/distribution` - Buat distribusi
- `PATCH /api/production/distribution` - Update status

### Returns:
- `GET /api/production/returns` - List returns
- `POST /api/production/returns` - Proses return

### Analytics:
- `GET /api/production/analytics` - Data analisis
- `GET /api/production/dashboard` - Dashboard data

---

## 9. TROUBLESHOOTING

### Masalah Umum:

1. **Stock tidak tersedia**:
   - Cek apakah produksi sudah di-sync
   - Periksa reserved quantity

2. **Distribusi kosong di returns**:
   - Pastikan distribusi sudah di-distribute
   - Hanya status `distributed` yang bisa di-return

3. **Bad stock tidak muncul**:
   - Periksa tanggal produksi
   - Pastikan data produksi sudah di-sync

4. **Return tidak mempengaruhi stock**:
   - Periksa action (harus `restock`)
   - Periksa condition (harus `good`)
